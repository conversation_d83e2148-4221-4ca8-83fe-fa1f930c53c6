"use client";

import { Loader2 } from "lucide-react";
import type React from "react";
import FeaturedMenuItem from "@/components/featured-menu-item";
import { useState, useEffect, Suspense } from "react";
import AddressAutocomplete from "@/components/address-autocomplete";
import { mapProductToMenuItem } from "@/services/api";
import { useCart } from "@/context/cart-context";
import { useBusiness } from "@/context/business-context";
import { useRouter } from "next/navigation";
import { useToast } from "@/context/toast-context";
import HomePageSkeleton from "@/components/home-page-skeleton";
import DeliveryTimingSelect from "@/components/delivery-timing-select";
import { LocationPinIcon } from "@/components/icons/location-pin-icon";
import dynamic from "next/dynamic";
import NewsletterSignup from "@/components/newsletter-signup";
import { useAuth } from "@/context/auth-context";
import AuthModalWrapper from "@/components/auth-modal-wrapper";
import ScheduleModalWrapper from "@/components/schedule-modal-wrapper";
import { validateDeliveryAddress } from "@/services/address-validation-api";

// Dynamically import Slider with no SSR to avoid hydration issues
const Slider = dynamic(() => import("react-slick"), { ssr: false });

interface ClientHomePageProps {
  initialH1: string | null;
}

export default function ClientHomePage({ initialH1 }: ClientHomePageProps) {
  const [featuredMenuItems, setFeaturedMenuItems] = useState<
    ReturnType<typeof mapProductToMenuItem>[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authModalView, setAuthModalView] = useState<
    "login" | "signup" | "forgot-password"
  >("signup");
  const [pickupError, setPickupError] = useState<string | null>(null);
  const [isValidatingAddress, setIsValidatingAddress] = useState(false);
  const {
    orderType,
    setOrderType,
    deliveryTiming,
    setDeliveryTiming,
    deliveryAddress,
    setDeliveryAddress,
    scheduledDateTime,
    setScheduledDateTime,
    orderSchedule,
    setOrderSchedule,
    addressError,
    setAddressError,
    addressDetails,
    userLocation,
  } = useCart();
  const {
    businessData,
    selectedBranch,
    setSelectedBranch,
    isLoading: isBusinessLoading,
    isBranchOpen,
    getBranchStatus,
  } = useBusiness();
  const router = useRouter();
  const { showToast } = useToast();
  const { isAuthenticated } = useAuth(); // Moved hook to the top level
  const [scheduleModalOpen, setScheduleModalOpen] = useState(false);

  // Add this useEffect near the top of the Home component function, after the hooks declarations
  useEffect(() => {
    // Check if there's a success message from contact form submission
    const successMessage = sessionStorage.getItem("contactFormSuccess");
    if (successMessage) {
      // Display the success message in a toast
      showToast({
        message: successMessage,
        type: "success",
        duration: 5000,
      });

      // Remove the message from storage to prevent showing it again on refresh
      sessionStorage.removeItem("contactFormSuccess");
    }
  }, [showToast]);

  useEffect(() => {
    async function fetchFeaturedProducts() {
      try {
        setIsLoading(true);
        const response = await fetch("/api/featured-products");

        if (!response.ok) {
          throw new Error("Failed to fetch featured products");
        }

        const data = await response.json();
        const mappedItems = data.items.map(mapProductToMenuItem);
        setFeaturedMenuItems(mappedItems.slice(0, 8)); // Get only first 8 items
      } catch (err) {
        console.error("Error fetching featured products:", err);
        setError("Failed to load featured products. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    }

    fetchFeaturedProducts();
  }, []);

  // Clear errors when order type changes
  useEffect(() => {
    // Only clear the errors when switching between delivery and pickup
    // This way, if there's an address error and the user changes to pickup,
    // the address error will be cleared, but if they stay on delivery and
    // just change timing, the error will persist
    if (orderType === "delivery") {
      setPickupError(null);
      // Don't clear addressError here to preserve it when changing delivery options
    } else {
      setAddressError(null);
    }
  }, [orderType]);

  // Clear address error when address changes
  useEffect(() => {
    if (deliveryAddress) {
      // Only clear the address error if a new address is entered
      // This allows the error to persist until the user takes action
      setAddressError(null);
    }
  }, [deliveryAddress]);

  // Clear pickup error when branch changes
  useEffect(() => {
    if (selectedBranch) {
      setPickupError(null);
    }
  }, [selectedBranch]);

  const handleAddressSelect = (address: string) => {
    setDeliveryAddress(address);
    if (address) {
      setAddressError(null);
    }
  };

  // Function to validate delivery/pickup details
  const validateDeliveryDetails = async () => {
    // For delivery, check if address is entered
    if (orderType === "delivery") {
      if (!deliveryAddress) {
        setAddressError("Please enter a delivery address");
        return false;
      }

      // If there's already an address error, return false
      if (addressError) {
        return false;
      }

      // Validate the delivery address with the API
      if (userLocation.lat && userLocation.lng) {
        setIsValidatingAddress(true);
        try {
          const validationResult = await validateDeliveryAddress(
            userLocation,
            addressDetails
          );

          if (!validationResult.isValid) {
            setAddressError(
              validationResult.message ||
                "This address is outside our delivery area."
            );
            return false;
          }
        } catch (error) {
          console.error("Address validation error:", error);
          setAddressError(
            "Unable to validate delivery address. Please try again."
          );
          return false;
        } finally {
          setIsValidatingAddress(false);
        }
      }

      return true;
    }

    // For pickup, check if a branch is selected
    if (orderType === "pickup") {
      if (!selectedBranch) {
        setPickupError("Please select a pickup location");
        return false;
      }
      return true;
    }

    return false;
  };

  // Handle View Full Menu button click
  const handleViewFullMenuClick = async (e: React.MouseEvent) => {
    e.preventDefault();

    // Check if delivery/pickup details are valid
    const isValid = await validateDeliveryDetails();

    // If valid, navigate to menu page, otherwise stay on our-menu page
    if (isValid) {
      // If there's an address error, don't navigate to menu
      if (orderType === "delivery" && addressError) {
        router.push("/our-menu");
      } else {
        router.push("/menu");
      }
    } else {
      // Navigate to our-menu page
      router.push("/our-menu");
    }
  };

  // Update the validateAndProceed function to check if a schedule has been selected
  // for "later", "weekly", or "biweekly" delivery timing options

  // Find the validateAndProceed function and replace it with this updated version:
  const validateAndProceed = async () => {
    setIsSubmitting(true);

    try {
      // Validate based on order type
      if (orderType === "delivery") {
        if (!deliveryAddress) {
          setAddressError("Please enter a delivery address");
          setIsSubmitting(false);
          return;
        }

        // Validate the delivery address with the API
        if (userLocation.lat && userLocation.lng) {
          setIsValidatingAddress(true);
          try {
            const validationResult = await validateDeliveryAddress(
              userLocation,
              addressDetails
            );

            if (!validationResult.isValid) {
              setAddressError(
                validationResult.message ||
                  "This address is outside our delivery area."
              );
              setIsSubmitting(false);
              setIsValidatingAddress(false);
              return;
            }
          } catch (error) {
            console.error("Address validation error:", error);
            setAddressError(
              "Unable to validate delivery address. Please try again."
            );
            setIsSubmitting(false);
            setIsValidatingAddress(false);
            return;
          } finally {
            setIsValidatingAddress(false);
          }
        }
      } else if (orderType === "pickup") {
        if (!selectedBranch) {
          setPickupError("Please select a pickup location");
          setIsSubmitting(false);
          return;
        }
      }

      // Validate delivery timing
      if (!deliveryTiming) {
        showToast({
          message: "Please select a delivery/pickup timing",
          type: "warning",
          duration: 4000,
        });
        setIsSubmitting(false);
        return;
      }

      // Check business hours for immediate orders
      console.log(
        "🏠 Home - Checking business hours for deliveryTiming:",
        deliveryTiming
      );
      if (deliveryTiming === "now") {
        console.log(
          "🏠 Home - Order is for 'now', checking if business is open..."
        );
        const isOpen = isBranchOpen();
        console.log("🏠 Home - Business open status:", isOpen);

        if (!isOpen) {
          console.log(
            "🏠 Home - Business is closed, showing toast and preventing navigation"
          );
          const status = getBranchStatus();
          const hoursInfo = status.todayHours
            ? ` Today's hours: ${status.todayHours}`
            : "";

          showToast({
            message: `Business is currently closed.${hoursInfo} Please schedule your order for later.`,
            type: "warning",
            duration: 6000,
          });
          setIsSubmitting(false);
          return;
        } else {
          console.log("🏠 Home - Business is open, proceeding with navigation");
        }
      } else {
        console.log(
          "🏠 Home - Order is scheduled for later, skipping business hours check"
        );
      }

      // Check if scheduled delivery option is selected
      if (
        deliveryTiming === "later" ||
        deliveryTiming === "weekly" ||
        deliveryTiming === "biweekly"
      ) {
        // For delivery, ensure address is entered and valid
        if (orderType === "delivery") {
          if (!deliveryAddress) {
            setAddressError("Please enter a delivery address");
            setIsSubmitting(false);
            return;
          }

          // If there's an address error, don't proceed with scheduling
          if (addressError) {
            setIsSubmitting(false);
            return;
          }
        }

        // For pickup, ensure branch is selected
        if (orderType === "pickup") {
          if (!selectedBranch) {
            setPickupError("Please select a pickup location");
            setIsSubmitting(false);
            return;
          }
        }

        // Check if a schedule has been selected
        if (!orderSchedule || !orderSchedule.date || !orderSchedule.time) {
          // No schedule selected, show the schedule modal
          setScheduleModalOpen(true);
          setIsSubmitting(false);
          return;
        }
      }

      // If all validations pass, redirect to menu page
      router.push("/menu");
    } catch (error) {
      console.error("Error during validation:", error);
      showToast({
        message: "An error occurred. Please try again",
        type: "error",
        duration: 4000,
      });
      setIsSubmitting(false);
    }
  };

  // Show skeleton while business data is loading
  if (isBusinessLoading) {
    return <HomePageSkeleton />;
  }

  return (
    <main className="">
      {/* Add this right after the opening <main> tag */}
      {initialH1 ? (
        <h1 className="sr-only">{initialH1}</h1>
      ) : (
        <h1 className="sr-only">
          Tiffin Service and Food Delivery in Canada at EZeats
        </h1>
      )}

      {/* Hero Section - Full width */}
      <section className="relative w-full">
        <div className="w-full relative">
          {/* Desktop Hero - Keep this unchanged */}
          <div className="h-full home_banner">
            <img
              src="https://static.tossdown.com/images/d11596f4-ad69-421b-843d-8adcfce86441.webp"
              alt="Banner desktop"
              className="banner_desk_img"
            />
            <img
              src="https://static.tossdown.com/images/2f8debaf-b350-42ca-b94e-b177d08fe7f6.webp"
              alt="Banner mobile"
              className="banner_mobile_img"
            />

            <div className="absolute inset-0 flex flex-col items-start justify-start">
              <div className="pl-[65px] pt-4 pb-4 pr-4 md:pt-8 md:pb-8 md:pr-8 mt-4 banner_form_box">
                <div className="max-w-md home_banner_form home_banner_text">
                  <h2>ORDER YOUR MEAL</h2>

                  {/* Delivery/Pickup Tabs */}
                  <div className="banner_tabs w-full ">
                    <button
                      className={`flex-1 py-1.5 px-3 text-sm font-medium text-center rounded-full transition-colors ${
                        orderType === "delivery"
                          ? "bg-black text-white shadow-md"
                          : ""
                      }`}
                      onClick={() => setOrderType("delivery")}
                    >
                      Delivery
                    </button>
                    <button
                      className={`flex-1 py-1.5 px-3 text-sm font-medium text-center rounded-full transition-colors ${
                        orderType === "pickup"
                          ? "bg-black text-white shadow-md"
                          : ""
                      }`}
                      onClick={() => setOrderType("pickup")}
                    >
                      Pickup
                    </button>
                  </div>

                  <div className="space-y-3 home_banner_fields">
                    {orderType === "pickup" ? (
                      <div>
                        <div className="relative">
                          <div className="relative banner_form_field">
                            <LocationPinIcon
                              className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500"
                              size={20}
                            />
                            <select
                              className={`w-full md:w-[377px] h-[62px] p-2 pl-10 border ${
                                pickupError
                                  ? "border-red-500"
                                  : "border-gray-300"
                              } rounded-md appearance-none bg-white text-black font-poppins`}
                              onChange={(e) => {
                                const branchId = Number.parseInt(
                                  e.target.value
                                );
                                const branch =
                                  businessData?.result?.branches?.find(
                                    (b) => b.id === branchId
                                  );
                                if (branch) setSelectedBranch(branch);
                              }}
                              value={selectedBranch?.id || ""}
                            >
                              <option value="" disabled>
                                Select pickup location
                              </option>
                              {businessData?.result?.branches
                                ?.filter((branch) => branch.pickup === 1)
                                .map((branch) => (
                                  <option key={branch.id} value={branch.id}>
                                    {branch.address}, {branch.city}
                                  </option>
                                ))}
                            </select>
                            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                              <svg
                                className="h-5 w-5 text-gray-400"
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                              >
                                <path
                                  fillRule="evenodd"
                                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                  clipRule="evenodd"
                                />
                              </svg>
                            </div>
                          </div>
                          {pickupError && (
                            <div className="text-red-500 text-sm mt-1">
                              {pickupError}
                            </div>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div className="relative banner_form_field">
                        <AddressAutocomplete
                          onAddressSelect={handleAddressSelect}
                          placeholder="Enter delivery address"
                          value={deliveryAddress}
                          hasError={!!addressError}
                        />
                        {addressError && (
                          <div className="bg-red-500 text-white text-sm mt-1 px-3 py-1 rounded -mb-2">
                            {addressError}
                          </div>
                        )}
                      </div>
                    )}
                    <div className="flex gap-2 banner_submit_btn">
                      <div className="relative flex-1 dropdown_toggle">
                        <DeliveryTimingSelect
                          value={deliveryTiming}
                          onChange={setDeliveryTiming}
                          orderType={orderType}
                        />
                      </div>
                      <button
                        onClick={validateAndProceed}
                        disabled={isSubmitting || isValidatingAddress}
                        className="bg-black text-white px-6 py-2 rounded-md hover:bg-gray-800 transition-colors disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center w-[120px] font-poppins banner_form_submit_btn"
                      >
                        {isSubmitting || isValidatingAddress ? (
                          <Loader2 size={20} className="animate-spin" />
                        ) : (
                          "ENTER"
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section - Constrained width */}
      <section className="custom_2_parent">
        <div className="custom_2_header">
          <small>
            <strong></strong> <span></span>
            <span></span>
            <span></span>
          </small>
          <h3>HOW IT WORKS</h3>
          <small>
            {" "}
            <span></span>
            <span></span>
            <span></span>
            <strong></strong>
          </small>
        </div>
        <div className="custom_2_body">
          <div className="custom_container">
            <div className="custom_2_data">
              <div className="custom_2_single">
                <figure>
                  <img
                    src="https://static.tossdown.com/images/010feec2-0544-4f83-9748-cb9c374bdc10.webp"
                    alt="Enter Address"
                  />
                </figure>
                <span>Enter Address</span>
              </div>
              <div className="custom_2_single">
                <figure>
                  <img
                    src="https://static.tossdown.com/images/ac0c42a7-02cc-4165-af98-e44ee0cc8506.webp"
                    alt="Schedule Delivery Type"
                  />
                </figure>
                <span>Schedule Delivery Type</span>
              </div>
              <div className="custom_2_single">
                <figure>
                  <img
                    src="https://static.tossdown.com/images/8bbc0bbe-dccf-42b4-890c-4a50c594eb0a.webp"
                    alt="Select Your Meals"
                  />
                </figure>
                <span>Select Your Meals</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Menu Section - Constrained width */}
      <section className="custom_3_parent">
        <div className="custom_3_header">
          <small>
            <strong></strong> <span></span>
            <span></span>
            <span></span>
          </small>
          <h3>Menu</h3>
          <small>
            {" "}
            <span></span>
            <span></span>
            <span></span>
            <strong></strong>
          </small>
        </div>
      </section>

      <section>
        <div className="container mx-auto px-4 max-w-7xl">
          <Suspense
            fallback={
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-10">
                {[...Array(8)].map((_, index) => (
                  <div key={index} className="animate-pulse">
                    <div className="bg-gray-200 aspect-square w-full mb-3"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mb-2"></div>
                    <div className="flex justify-center mb-2">
                      {[...Array(5)].map((_, i) => (
                        <div
                          key={i}
                          className="w-5 h-5 bg-gray-200 rounded-full mx-0.5"
                        ></div>
                      ))}
                    </div>
                    <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
                  </div>
                ))}
              </div>
            }
          >
            {isLoading ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-10">
                {[...Array(8)].map((_, index) => (
                  <div key={index} className="animate-pulse">
                    <div className="bg-gray-200 aspect-square w-full mb-3"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mb-2"></div>
                    <div className="flex justify-center mb-2">
                      {[...Array(5)].map((_, i) => (
                        <div
                          key={i}
                          className="w-5 h-5 bg-gray-200 rounded-full mx-0.5"
                        ></div>
                      ))}
                    </div>
                    <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
                  </div>
                ))}
              </div>
            ) : error ? (
              <div className="text-center text-red-500 py-10">{error}</div>
            ) : featuredMenuItems.length === 0 ? (
              <div className="text-center py-10">
                No featured items available at the moment.
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-x-4 gap-y-10 md:gap-x-6 card_box_parent">
                {featuredMenuItems.map((item) => (
                  <FeaturedMenuItem
                    key={item.id}
                    id={item.id}
                    name={item.name}
                    image={item.image}
                    rating={item.rating}
                    price={item.price}
                    isVeg={item.isVeg}
                    inStock={item.inStock}
                  />
                ))}
              </div>
            )}
          </Suspense>

          <div className="menu_btn_box">
            <a href="#" onClick={handleViewFullMenuClick} className="menu_btn">
              View Full Menu
            </a>
          </div>
        </div>
      </section>

      {/* Add the NewsletterSignup component */}
      <NewsletterSignup />

      {/* Sign Up Now button - only shown when user is not authenticated */}
      {!isAuthenticated && (
        <div className="signup_btn mb-[60px]">
          <a
            href="#"
            id="signupClick"
            onClick={(e) => {
              e.preventDefault();
              // Show the signup modal
              setAuthModalView("signup");
              setAuthModalOpen(true);
            }}
          >
            Sign Up Now
          </a>
        </div>
      )}
      {/* Schedule Modal */}
      <ScheduleModalWrapper
        isOpen={scheduleModalOpen}
        onClose={() => setScheduleModalOpen(false)}
      />
      {/* Auth Modal */}
      <AuthModalWrapper
        isOpen={authModalOpen}
        initialView={authModalView}
        onClose={() => setAuthModalOpen(false)}
      />
    </main>
  );
}
