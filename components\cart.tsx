"use client";

import { useCart, type BoxSize } from "@/context/cart-context";
import {
  Plus,
  Minus,
  X,
  Trash2,
  Loader2,
  AlertCircle,
  ShoppingCart,
} from "lucide-react";
import Image from "next/image";
import { useState, useRef, useEffect } from "react";
import BoxSizeChangeConfirm from "./box-size-change-confirm";
import { useRouter, usePathname } from "next/navigation";
import { useToast } from "@/context/toast-context";
import { useBusiness } from "@/context/business-context";
import { trackInitiateCheckout } from "@/utils/analytics";
import BusinessHoursStatus from "./business-hours-status";

export default function Cart() {
  const {
    items,
    removeItem,
    updateQuantity,
    totalItems,
    totalPrice,
    boxSize,
    setBoxSize,
    isLoading,
    loadingItemId,
    loadingOperation,
  } = useCart();

  const { minimumSpend } = useBusiness();
  const currentPathname = usePathname();

  // Only show cart on the menu page
  const isMenuPage = currentPathname === "/menu";

  const [isOpen, setIsOpen] = useState(false);
  const [showBoxChangeConfirm, setShowBoxSizeChangeConfirm] = useState(false);
  const [pendingBoxSize, setPendingBoxSize] = useState<BoxSize>(null);
  const cartPanelRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const { showToast } = useToast();

  const toggleCart = () => {
    setIsOpen(!isOpen);
  };

  // Listen for custom event to toggle cart from header
  useEffect(() => {
    const handleToggleCart = () => {
      console.log("Toggle cart event received");
      setIsOpen(true);
    };

    document.addEventListener("toggleCart", handleToggleCart);

    return () => {
      document.removeEventListener("toggleCart", handleToggleCart);
    };
  }, []);

  // Handle click outside to close cart, but not when modals are open
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Only close if cart is open, click is outside cart panel, and no modals are open
      if (
        cartPanelRef.current &&
        !cartPanelRef.current.contains(event.target as Node) &&
        isOpen &&
        !showBoxChangeConfirm
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, showBoxChangeConfirm]);

  const handleBoxSizeClick = (size: BoxSize) => {
    // If there are items in the cart and the box size is changing
    if (items.length > 0 && boxSize !== size) {
      setPendingBoxSize(size);
      setShowBoxSizeChangeConfirm(true);
    } else {
      // If cart is empty or box size is the same, just set it
      setBoxSize(size);
    }
  };

  // Validate cart before proceeding to checkout
  const validateCartAndProceed = () => {
    // Check if box size is selected
    if (!boxSize) {
      showToast({
        message: "Please select a box size before proceeding.",
        type: "warning",
        duration: 4000,
      });
      return;
    }

    // Check if cart is empty
    if (items.length === 0) {
      showToast({
        message: "Your cart is empty. Please add items before proceeding.",
        type: "warning",
        duration: 4000,
      });
      return;
    }

    // Check if cart has the required number of items based on box size
    if (totalItems < boxSize) {
      showToast({
        message: `Your ${boxSize}-meal box requires ${boxSize} items. You currently have ${totalItems} items.`,
        type: "warning",
        duration: 4000,
      });
      return;
    }

    // Check if the order meets the minimum spend requirement
    if (minimumSpend > 0 && totalPrice < minimumSpend) {
      showToast({
        message: `Your order must be at least CAD ${minimumSpend.toFixed(
          2
        )} to proceed.`,
        type: "warning",
        duration: 4000,
      });
      return;
    }

    // Track InitiateCheckout event for Meta Pixel
    trackInitiateCheckout(
      items.map((item) => ({
        id: item.id,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        category: item.isVeg ? "Veg" : "Non-Veg",
      })),
      totalPrice
    );

    // If all validations pass, proceed to checkout
    setIsOpen(false);
    router.push("/checkout");
  };

  // Determine if the Place Order button should be disabled
  const isPlaceOrderDisabled =
    !boxSize ||
    items.length === 0 ||
    isLoading ||
    totalItems < boxSize ||
    totalItems > boxSize ||
    (minimumSpend > 0 && totalPrice < minimumSpend);

  // Calculate how much more needed to meet minimum spend
  const amountNeededForMinimumSpend =
    minimumSpend > 0 && totalPrice < minimumSpend
      ? minimumSpend - totalPrice
      : 0;

  // Always render the cart component, but only show the floating button on menu page
  const showFloatingButton = isMenuPage;

  return (
    <div className="relative">
      {!isOpen && showFloatingButton && (
        <button
          onClick={toggleCart}
          className="fixed bottom-6 right-6 z-50 bg-black text-white p-5 rounded-full shadow-lg flex flex-col items-center justify-center w-24 h-24"
        >
          <img
            src="https://static.tossdown.com/images/14f80f30-18d1-41c2-ae64-8a009b7ee62b.webp"
            alt="Cart"
            width={48}
            height={48}
          />
          <span className="text-lg font-medium mt-1">
            {totalItems}/{boxSize || 0}
          </span>
        </button>
      )}

      {/* Box Size Change Confirmation */}
      <BoxSizeChangeConfirm
        isOpen={showBoxChangeConfirm}
        onClose={() => setShowBoxSizeChangeConfirm(false)}
        newBoxSize={pendingBoxSize}
      />

      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-[11000] flex justify-end">
          <div
            ref={cartPanelRef}
            className="bg-white w-full max-w-md h-full flex flex-col"
          >
            {/* Cart Header */}
            <div className="p-5 border-b">
              <div className="flex justify-between items-center mb-3">
                <h2 className="text-2xl font-normal uppercase">Your Cart</h2>
                <button onClick={toggleCart} className="text-black">
                  <X size={24} />
                </button>
              </div>
              {/* Business Hours Status */}
              <BusinessHoursStatus showDetails={true} />
            </div>

            {/* Box Size Selection - Always visible */}
            <div className="p-5 border-b">
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3">
                  <p className="font-bold text-lg mb-2 sm:mb-0">
                    Choose your box size
                  </p>
                </div>

                {/* Updated Tab UI for box size selection */}
                <div className="flex w-full bg-gray-200 rounded-full p-1 mb-3">
                  <button
                    className={`flex-1 py-3 px-6 font-bold text-center rounded-full transition-colors ${
                      boxSize === 6 ? "bg-black text-white shadow-md" : ""
                    }`}
                    onClick={() => handleBoxSizeClick(6)}
                  >
                    6
                  </button>
                  <button
                    className={`flex-1 py-3 px-6 font-bold text-center rounded-full transition-colors ${
                      boxSize === 12 ? "bg-black text-white shadow-md" : ""
                    }`}
                    onClick={() => handleBoxSizeClick(12)}
                  >
                    12
                  </button>
                </div>
              </div>
            </div>

            {items.length === 0 ? (
              <div className="flex-1 flex flex-col items-center justify-center p-5">
                <ShoppingCart size={64} className="text-gray-300 mb-4" />
                <p className="text-gray-500">Your cart is empty</p>
              </div>
            ) : (
              <>
                {/* Cart Items */}
                <div className="flex-1 overflow-y-auto">
                  {items.map((item) => {
                    const isItemLoading =
                      isLoading && loadingItemId === item.id;
                    const isAddLoading =
                      isItemLoading && loadingOperation === "add";
                    const isRemoveLoading =
                      isItemLoading && loadingOperation === "remove";
                    const isDeleteLoading =
                      isItemLoading && loadingOperation === "remove";

                    return (
                      <div
                        key={item.id}
                        className="p-5 border-b flex items-center"
                      >
                        <div className="w-20 h-[92px] relative mr-4 overflow-hidden">
                          <Image
                            src={item.image || "/placeholder.svg"}
                            alt={item.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start mb-2">
                            <h3
                              className="text-[#212121] capitalize mb-2"
                              style={{
                                fontFamily: "Poppins",
                                fontSize: "16px",
                                fontWeight: 400,
                                lineHeight: "22px",
                                letterSpacing: "0em",
                                height: "44px",
                                maxWidth: "140px",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                wordWrap: "break-word",
                                display: "-webkit-box",
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: "vertical",
                              }}
                            >
                              {item.name}
                            </h3>
                            <p className="font-medium text-gray-800">
                              CAD {item.price.toFixed(2)}
                            </p>
                          </div>
                          <div className="flex items-center">
                            <div className="flex items-center bg-gray-100 rounded-md">
                              <button
                                onClick={() =>
                                  updateQuantity(item.id, item.quantity - 1)
                                }
                                className="p-2 flex items-center justify-center w-8 h-8"
                                disabled={isItemLoading}
                              >
                                {isRemoveLoading ? (
                                  <Loader2 size={16} className="animate-spin" />
                                ) : (
                                  <Minus size={16} />
                                )}
                              </button>
                              <div className="px-3 py-1 flex items-center justify-center">
                                {item.quantity}
                              </div>
                              <button
                                onClick={() =>
                                  updateQuantity(item.id, item.quantity + 1)
                                }
                                className="p-2 flex items-center justify-center w-8 h-8"
                                disabled={isItemLoading}
                              >
                                {isAddLoading ? (
                                  <Loader2 size={16} className="animate-spin" />
                                ) : (
                                  <Plus size={16} />
                                )}
                              </button>
                            </div>
                            <button
                              onClick={() => removeItem(item.id)}
                              className="ml-2 p-2 flex items-center justify-center w-8 h-8 bg-gray-100 rounded-md"
                              disabled={isItemLoading}
                            >
                              {isDeleteLoading ? (
                                <Loader2 size={16} className="animate-spin" />
                              ) : (
                                <Trash2 size={16} />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Cart Footer - Simplified */}
                <div className="p-5 border-t mt-auto">
                  {/* Box size warning */}
                  {boxSize && totalItems < boxSize && (
                    <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md flex items-start">
                      <AlertCircle
                        size={18}
                        className="text-yellow-500 mr-2 flex-shrink-0 mt-0.5"
                      />
                      <p className="text-sm text-yellow-700">
                        Your {boxSize}-meal box requires {boxSize} items. Please
                        add {boxSize - totalItems} more{" "}
                        {boxSize - totalItems === 1 ? "item" : "items"}.
                      </p>
                    </div>
                  )}

                  {/* Minimum spend warning */}
                  {minimumSpend > 0 && totalPrice < minimumSpend && (
                    <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md flex items-start">
                      <AlertCircle
                        size={18}
                        className="text-yellow-500 mr-2 flex-shrink-0 mt-0.5"
                      />
                      <p className="text-sm text-yellow-700">
                        Minimum order amount is CAD {minimumSpend.toFixed(2)}.
                        Add CAD {amountNeededForMinimumSpend.toFixed(2)} more to
                        proceed.
                      </p>
                    </div>
                  )}

                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">Subtotal</p>
                      <p className="text-xl font-bold">
                        CAD {totalPrice.toFixed(2)}
                      </p>
                    </div>
                    <button
                      onClick={validateCartAndProceed}
                      className={`font-bold py-3 px-8 rounded-full min-w-[140px] h-12 flex items-center justify-center ${
                        isPlaceOrderDisabled
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-black text-white hover:bg-gray-800"
                      }`}
                      disabled={isPlaceOrderDisabled}
                    >
                      {isLoading && loadingOperation === "clear" ? (
                        <Loader2 size={20} className="animate-spin" />
                      ) : (
                        "PLACE ORDER"
                      )}
                    </button>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
